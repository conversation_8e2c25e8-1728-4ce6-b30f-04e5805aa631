"""
RAG SQL系统使用示例和交互式演示
展示系统的各种功能和使用方法
"""

import os
import sys
import json
from typing import List, Dict
from rag_sql_system import RAGSQLSystem
from rag_sql_config import config

def interactive_demo():
    """交互式演示"""
    print("🤖 欢迎使用RAG SQL查询生成系统！")
    print("=" * 60)
    
    # 检查配置
    if not config.validate_config():
        print("\n❌ 配置验证失败，请检查环境变量设置")
        print("设置示例:")
        print("export OPENAI_API_KEY='your-openai-api-key'")
        return
    
    try:
        # 初始化系统
        print("\n📊 正在初始化系统...")
        system = RAGSQLSystem()
        print("✅ 系统初始化完成！")
        
        # 显示配置信息
        config.print_config_summary()
        
        print("\n" + "=" * 60)
        print("💡 使用说明:")
        print("1. 输入自然语言问题，系统会生成对应的SQL查询")
        print("2. 输入 'help' 查看帮助信息")
        print("3. 输入 'examples' 查看示例问题")
        print("4. 输入 'history' 查看对话历史")
        print("5. 输入 'clear' 清除对话历史")
        print("6. 输入 'quit' 或 'exit' 退出系统")
        print("=" * 60)
        
        while True:
            try:
                # 获取用户输入
                user_input = input("\n🔍 请输入您的问题: ").strip()
                
                if not user_input:
                    continue
                
                # 处理特殊命令
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 感谢使用RAG SQL系统，再见！")
                    break
                
                elif user_input.lower() == 'help':
                    show_help()
                    continue
                
                elif user_input.lower() == 'examples':
                    show_example_questions()
                    continue
                
                elif user_input.lower() == 'history':
                    show_conversation_history(system)
                    continue
                
                elif user_input.lower() == 'clear':
                    system.clear_conversation_history()
                    print("✅ 对话历史已清除")
                    continue
                
                # 生成SQL查询
                print("\n⏳ 正在生成SQL查询...")
                result = system.generate_sql(user_input)
                
                # 显示结果
                display_result(result)
                
            except KeyboardInterrupt:
                print("\n\n👋 用户中断，退出系统")
                break
            except Exception as e:
                print(f"\n❌ 处理过程中出现错误: {e}")
                continue
    
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")

def show_help():
    """显示帮助信息"""
    help_text = """
📚 RAG SQL系统帮助信息

🎯 主要功能:
- 自然语言转SQL查询
- 基于相似示例的智能提示
- 多轮对话支持
- 查询结果展示

💡 使用技巧:
1. 尽量使用清晰、具体的问题描述
2. 可以参考数据库中的表名和字段名
3. 支持复杂查询，如聚合、连接、子查询等
4. 系统会记住对话上下文，支持追问

📊 数据库结构:
- employees: 员工表 (id, name, department, salary, hire_date, age)
- departments: 部门表 (id, name, manager_id, budget)  
- projects: 项目表 (id, name, department_id, start_date, end_date, budget)

🔧 可用命令:
- help: 显示此帮助信息
- examples: 显示示例问题
- history: 查看对话历史
- clear: 清除对话历史
- quit/exit: 退出系统
"""
    print(help_text)

def show_example_questions():
    """显示示例问题"""
    examples = [
        "查询所有员工的姓名和薪资",
        "技术部有多少员工？",
        "哪个员工的薪资最高？",
        "查询每个部门的平均薪资",
        "查询2022年入职的员工信息",
        "查询年龄大于30岁的员工",
        "查询各部门的预算总和",
        "查询参与项目的员工信息",
        "查询薪资排名前3的员工",
        "查询每个部门薪资最高的员工"
    ]
    
    print("\n📝 示例问题:")
    for i, example in enumerate(examples, 1):
        print(f"  {i}. {example}")

def show_conversation_history(system: RAGSQLSystem):
    """显示对话历史"""
    history = system.get_conversation_history()
    
    if not history:
        print("📝 暂无对话历史")
        return
    
    print(f"\n📝 对话历史 (最近{len(history)}条):")
    print("-" * 50)
    
    for i, entry in enumerate(history, 1):
        role = "用户" if entry['type'] == 'human' else "助手"
        content = entry['content'][:100] + "..." if len(entry['content']) > 100 else entry['content']
        print(f"{i}. [{role}] {content}")

def display_result(result: Dict):
    """显示查询结果"""
    print("\n" + "=" * 60)
    
    if result['success']:
        print("✅ SQL生成成功！")
        print(f"\n📝 生成的SQL:")
        print(f"   {result['generated_sql']}")
        
        print(f"\n📊 查询结果:")
        print(result['result_summary'])
        
        # 显示相似示例
        if result['similar_examples']:
            print(f"\n🔍 参考的相似示例:")
            for i, example in enumerate(result['similar_examples'][:2], 1):
                print(f"  {i}. {example['question']}")
        
        # 显示token使用情况
        if result.get('token_usage'):
            usage = result['token_usage']
            print(f"\n💰 资源使用:")
            print(f"   Tokens: {usage.get('total_tokens', 0)}")
            print(f"   成本: ${usage.get('total_cost', 0):.4f}")
    else:
        print("❌ SQL生成失败")
        print(f"错误信息: {result.get('error', '未知错误')}")
    
    print("=" * 60)

def batch_test_demo():
    """批量测试演示"""
    print("🧪 批量测试演示")
    print("=" * 40)
    
    # 测试问题集
    test_questions = [
        {"question": "查询所有员工", "expected_contains": "SELECT"},
        {"question": "技术部员工数量", "expected_contains": "COUNT"},
        {"question": "薪资最高的员工", "expected_contains": "MAX"},
        {"question": "各部门平均薪资", "expected_contains": "AVG"},
        {"question": "2022年入职员工", "expected_contains": "2022"}
    ]
    
    try:
        system = RAGSQLSystem()
        
        print(f"📊 开始测试 {len(test_questions)} 个问题...")
        
        results = []
        for i, test_case in enumerate(test_questions, 1):
            print(f"\n测试 {i}/{len(test_questions)}: {test_case['question']}")
            
            result = system.generate_sql(test_case['question'], use_memory=False)
            
            # 简单验证
            success = result['success']
            if success and test_case.get('expected_contains'):
                success = test_case['expected_contains'].upper() in result['generated_sql'].upper()
            
            results.append({
                'question': test_case['question'],
                'success': success,
                'sql': result['generated_sql'] if result['success'] else 'N/A'
            })
            
            status = "✅" if success else "❌"
            print(f"  {status} {result['generated_sql'][:50]}...")
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        success_rate = success_count / len(results) * 100
        
        print(f"\n📈 测试结果统计:")
        print(f"  总问题数: {len(results)}")
        print(f"  成功数量: {success_count}")
        print(f"  成功率: {success_rate:.1f}%")
        
    except Exception as e:
        print(f"❌ 批量测试失败: {e}")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == 'test':
            batch_test_demo()
        elif sys.argv[1] == 'interactive':
            interactive_demo()
        else:
            print("用法: python rag_sql_demo.py [interactive|test]")
    else:
        interactive_demo()

if __name__ == "__main__":
    main()
