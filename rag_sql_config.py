"""
RAG SQL系统配置文件
包含系统的各种配置参数和设置
"""

import os
from typing import Dict, Any

class RAGSQLConfig:
    """RAG SQL系统配置类"""
    
    # OpenAI配置
    OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY", "")
    OPENAI_MODEL = "gpt-3.5-turbo"
    OPENAI_TEMPERATURE = 0.1
    OPENAI_MAX_TOKENS = 1000
    
    # 数据库配置
    DATABASE_PATH = "sample_database.db"
    DATABASE_TYPE = "sqlite"  # sqlite, mysql, postgresql
    
    # MySQL配置（如果使用MySQL）
    MYSQL_CONFIG = {
        "host": os.environ.get("MYSQL_HOST", "localhost"),
        "port": int(os.environ.get("MYSQL_PORT", "3306")),
        "user": os.environ.get("MYSQL_USER", "root"),
        "password": os.environ.get("MYSQL_PASSWORD", ""),
        "database": os.environ.get("MYSQL_DATABASE", "test_db")
    }
    
    # ChromaDB配置
    CHROMA_PERSIST_DIRECTORY = "./chroma_db"
    CHROMA_COLLECTION_NAME = "sql_examples"
    
    # 向量检索配置
    SIMILARITY_SEARCH_K = 3  # 检索相似示例的数量
    EMBEDDING_MODEL = "text-embedding-ada-002"
    
    # 对话记忆配置
    MEMORY_WINDOW_SIZE = 5  # 保持的对话轮数
    
    # 系统提示词配置
    SYSTEM_PROMPT_TEMPLATE = """你是一个专业的SQL查询生成助手。你的任务是根据用户的自然语言问题生成准确的SQL查询语句。

请遵循以下规则：
1. 仔细分析用户问题，理解查询意图
2. 根据提供的数据库结构信息生成SQL
3. 参考相似示例的模式和语法
4. 确保SQL语法正确且符合{database_type}标准
5. 只返回SQL语句，不要包含其他解释文字
6. 使用适当的表连接、聚合函数和条件过滤
7. 注意字段名和表名的准确性

数据库结构信息：
{schema_info}

相似示例参考：
{similar_examples}

请根据以上信息生成准确的SQL查询。"""
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FILE = "rag_sql_system.log"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 性能配置
    MAX_RETRIES = 3  # 最大重试次数
    REQUEST_TIMEOUT = 30  # 请求超时时间（秒）
    
    # 评估配置
    EVALUATION_METRICS = [
        "sql_generation_success_rate",
        "sql_execution_success_rate", 
        "average_token_usage",
        "average_response_time",
        "total_cost"
    ]
    
    @classmethod
    def get_openai_config(cls) -> Dict[str, Any]:
        """获取OpenAI配置"""
        return {
            "model_name": cls.OPENAI_MODEL,
            "temperature": cls.OPENAI_TEMPERATURE,
            "max_tokens": cls.OPENAI_MAX_TOKENS,
            "openai_api_key": cls.OPENAI_API_KEY
        }
    
    @classmethod
    def get_database_config(cls) -> Dict[str, Any]:
        """获取数据库配置"""
        if cls.DATABASE_TYPE == "mysql":
            return cls.MYSQL_CONFIG
        else:
            return {"path": cls.DATABASE_PATH}
    
    @classmethod
    def get_chroma_config(cls) -> Dict[str, Any]:
        """获取ChromaDB配置"""
        return {
            "persist_directory": cls.CHROMA_PERSIST_DIRECTORY,
            "collection_name": cls.CHROMA_COLLECTION_NAME
        }
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置是否完整"""
        required_configs = [
            cls.OPENAI_API_KEY,
        ]
        
        missing_configs = [config for config in required_configs if not config]
        
        if missing_configs:
            print("❌ 缺少必要的配置:")
            if not cls.OPENAI_API_KEY:
                print("  - OPENAI_API_KEY 环境变量未设置")
            return False
        
        return True
    
    @classmethod
    def print_config_summary(cls):
        """打印配置摘要"""
        print("📋 RAG SQL系统配置摘要:")
        print(f"  OpenAI模型: {cls.OPENAI_MODEL}")
        print(f"  数据库类型: {cls.DATABASE_TYPE}")
        print(f"  向量数据库: ChromaDB ({cls.CHROMA_PERSIST_DIRECTORY})")
        print(f"  相似检索数量: {cls.SIMILARITY_SEARCH_K}")
        print(f"  对话记忆窗口: {cls.MEMORY_WINDOW_SIZE}")
        print(f"  日志级别: {cls.LOG_LEVEL}")


# 创建全局配置实例
config = RAGSQLConfig()

# 环境变量配置映射
ENV_CONFIG_MAPPING = {
    "OPENAI_API_KEY": "OPENAI_API_KEY",
    "OPENAI_MODEL": "OPENAI_MODEL", 
    "DATABASE_PATH": "DATABASE_PATH",
    "DATABASE_TYPE": "DATABASE_TYPE",
    "CHROMA_PERSIST_DIRECTORY": "CHROMA_PERSIST_DIRECTORY",
    "SIMILARITY_SEARCH_K": "SIMILARITY_SEARCH_K",
    "MEMORY_WINDOW_SIZE": "MEMORY_WINDOW_SIZE",
    "LOG_LEVEL": "LOG_LEVEL"
}

def load_config_from_env():
    """从环境变量加载配置"""
    for env_var, config_attr in ENV_CONFIG_MAPPING.items():
        env_value = os.environ.get(env_var)
        if env_value:
            # 处理数值类型
            if config_attr in ["SIMILARITY_SEARCH_K", "MEMORY_WINDOW_SIZE"]:
                env_value = int(env_value)
            elif config_attr in ["OPENAI_TEMPERATURE"]:
                env_value = float(env_value)
            
            setattr(config, config_attr, env_value)

# 自动加载环境变量配置
load_config_from_env()
