"""
RAG SQL系统快速启动版本
简化版本，便于快速测试和演示
"""

import os
import json
import sqlite3
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

# 检查是否有必要的包
try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False
    print("⚠️ 未安装openai包，将使用模拟模式")

class SimpleDatabaseManager:
    """简化的数据库管理器"""
    
    def __init__(self, db_path: str = "simple_rag_db.db"):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建员工表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                department TEXT NOT NULL,
                salary REAL NOT NULL,
                hire_date DATE NOT NULL,
                age INTEGER NOT NULL
            )
        """)
        
        # 插入示例数据
        sample_data = [
            (1, '张三', '技术部', 8000.0, '2022-01-15', 28),
            (2, '李四', '销售部', 6500.0, '2021-03-20', 32),
            (3, '王五', '人事部', 7000.0, '2020-06-10', 35),
            (4, '赵六', '技术部', 9500.0, '2019-08-05', 30),
            (5, '钱七', '财务部', 7500.0, '2021-11-12', 29)
        ]
        
        cursor.executemany("""
            INSERT OR REPLACE INTO employees (id, name, department, salary, hire_date, age)
            VALUES (?, ?, ?, ?, ?, ?)
        """, sample_data)
        
        conn.commit()
        conn.close()
        print("✅ 数据库初始化完成")
    
    def get_schema_info(self) -> str:
        """获取数据库结构信息"""
        return """
数据库结构：
表名: employees (员工表)
字段:
- id: 员工ID (整数, 主键)
- name: 姓名 (文本)
- department: 部门 (文本)
- salary: 薪资 (浮点数)
- hire_date: 入职日期 (日期)
- age: 年龄 (整数)

示例数据:
- 张三, 技术部, 8000元, 28岁
- 李四, 销售部, 6500元, 32岁
- 王五, 人事部, 7000元, 35岁
- 赵六, 技术部, 9500元, 30岁
- 钱七, 财务部, 7500元, 29岁
"""
    
    def execute_sql(self, sql: str) -> Tuple[List[Dict], str]:
        """执行SQL查询"""
        try:
            conn = sqlite3.connect(self.db_path)
            df = pd.read_sql_query(sql, conn)
            results = df.to_dict('records')
            conn.close()
            
            if results:
                result_str = f"查询成功，返回{len(results)}条记录：\n"
                result_str += df.to_string(index=False)
            else:
                result_str = "查询成功，但没有找到匹配的记录。"
            
            return results, result_str
        except Exception as e:
            return [], f"SQL执行失败: {str(e)}"

class SimpleExampleManager:
    """简化的示例管理器"""
    
    def __init__(self):
        self.examples = [
            {
                "question": "查询所有员工的姓名和薪资",
                "sql": "SELECT name, salary FROM employees;",
                "keywords": ["所有", "员工", "姓名", "薪资"]
            },
            {
                "question": "查询技术部门的所有员工",
                "sql": "SELECT * FROM employees WHERE department = '技术部';",
                "keywords": ["技术部", "员工"]
            },
            {
                "question": "查询薪资大于8000的员工",
                "sql": "SELECT name, department, salary FROM employees WHERE salary > 8000;",
                "keywords": ["薪资", "大于", "8000"]
            },
            {
                "question": "按部门统计员工数量",
                "sql": "SELECT department, COUNT(*) as employee_count FROM employees GROUP BY department;",
                "keywords": ["部门", "统计", "数量", "员工数"]
            },
            {
                "question": "查询每个部门的平均薪资",
                "sql": "SELECT department, AVG(salary) as avg_salary FROM employees GROUP BY department;",
                "keywords": ["部门", "平均", "薪资"]
            },
            {
                "question": "查询薪资最高的员工",
                "sql": "SELECT * FROM employees WHERE salary = (SELECT MAX(salary) FROM employees);",
                "keywords": ["薪资", "最高", "员工"]
            },
            {
                "question": "查询年龄大于30的员工数量",
                "sql": "SELECT COUNT(*) as count FROM employees WHERE age > 30;",
                "keywords": ["年龄", "大于", "30", "数量"]
            }
        ]
    
    def find_similar_examples(self, question: str, k: int = 3) -> List[Dict]:
        """基于关键词匹配找到相似示例"""
        question_lower = question.lower()
        scored_examples = []
        
        for example in self.examples:
            score = 0
            for keyword in example['keywords']:
                if keyword in question_lower:
                    score += 1
            
            if score > 0:
                scored_examples.append((score, example))
        
        # 按分数排序并返回前k个
        scored_examples.sort(key=lambda x: x[0], reverse=True)
        return [example for _, example in scored_examples[:k]]

class SimpleRAGSQLSystem:
    """简化的RAG SQL系统"""
    
    def __init__(self, openai_api_key: str = None):
        self.db_manager = SimpleDatabaseManager()
        self.example_manager = SimpleExampleManager()
        self.openai_api_key = openai_api_key or os.environ.get("OPENAI_API_KEY")
        
        if HAS_OPENAI and self.openai_api_key:
            openai.api_key = self.openai_api_key
            self.use_openai = True
            print("✅ 使用OpenAI模式")
        else:
            self.use_openai = False
            print("⚠️ 使用规则模式（无需API密钥）")
    
    def generate_sql_with_openai(self, question: str, similar_examples: List[Dict]) -> str:
        """使用OpenAI生成SQL"""
        schema_info = self.db_manager.get_schema_info()
        
        # 构建示例文本
        examples_text = ""
        if similar_examples:
            examples_text = "\n参考示例：\n"
            for i, example in enumerate(similar_examples, 1):
                examples_text += f"{i}. 问题: {example['question']}\n"
                examples_text += f"   SQL: {example['sql']}\n"
        
        prompt = f"""你是一个SQL查询生成专家。根据用户问题生成准确的SQL查询语句。

{schema_info}

{examples_text}

用户问题: {question}

请生成对应的SQL查询语句（只返回SQL，不要其他解释）："""
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=200,
                temperature=0.1
            )
            
            sql = response.choices[0].message.content.strip()
            # 清理SQL
            if sql.startswith('```sql'):
                sql = sql[6:]
            elif sql.startswith('```'):
                sql = sql[3:]
            if sql.endswith('```'):
                sql = sql[:-3]
            
            sql = sql.strip()
            if not sql.endswith(';'):
                sql += ';'
            
            return sql
        except Exception as e:
            print(f"OpenAI调用失败: {e}")
            return self.generate_sql_with_rules(question, similar_examples)
    
    def generate_sql_with_rules(self, question: str, similar_examples: List[Dict]) -> str:
        """使用规则生成SQL"""
        question_lower = question.lower()
        
        # 如果有相似示例，直接使用最相似的
        if similar_examples:
            return similar_examples[0]['sql']
        
        # 基础规则匹配
        if "所有员工" in question_lower or "全部员工" in question_lower:
            return "SELECT * FROM employees;"
        elif "员工数量" in question_lower or "多少员工" in question_lower:
            if "技术部" in question_lower:
                return "SELECT COUNT(*) as count FROM employees WHERE department = '技术部';"
            else:
                return "SELECT COUNT(*) as count FROM employees;"
        elif "平均薪资" in question_lower:
            return "SELECT department, AVG(salary) as avg_salary FROM employees GROUP BY department;"
        elif "最高薪资" in question_lower or "薪资最高" in question_lower:
            return "SELECT * FROM employees WHERE salary = (SELECT MAX(salary) FROM employees);"
        else:
            return "SELECT * FROM employees LIMIT 5;"
    
    def generate_sql(self, question: str) -> Dict[str, Any]:
        """生成SQL查询"""
        print(f"🔍 处理问题: {question}")
        
        # 1. 查找相似示例
        similar_examples = self.example_manager.find_similar_examples(question)
        print(f"📚 找到{len(similar_examples)}个相似示例")
        
        # 2. 生成SQL
        if self.use_openai:
            sql = self.generate_sql_with_openai(question, similar_examples)
        else:
            sql = self.generate_sql_with_rules(question, similar_examples)
        
        print(f"📝 生成SQL: {sql}")
        
        # 3. 执行SQL
        results, result_str = self.db_manager.execute_sql(sql)
        
        return {
            'question': question,
            'generated_sql': sql,
            'execution_results': results,
            'result_summary': result_str,
            'similar_examples': similar_examples,
            'success': len(results) >= 0
        }

def demo_simple_rag_sql():
    """演示简化版RAG SQL系统"""
    print("🚀 简化版RAG SQL系统演示")
    print("=" * 50)
    
    # 初始化系统
    system = SimpleRAGSQLSystem()
    
    # 演示问题
    demo_questions = [
        "查询所有员工的信息",
        "技术部有多少员工？",
        "哪个员工的薪资最高？",
        "查询每个部门的平均薪资",
        "查询年龄大于30的员工"
    ]
    
    for i, question in enumerate(demo_questions, 1):
        print(f"\n问题 {i}: {question}")
        print("-" * 40)
        
        result = system.generate_sql(question)
        
        if result['success']:
            print(f"✅ SQL: {result['generated_sql']}")
            print(f"📊 结果:\n{result['result_summary']}")
            
            if result['similar_examples']:
                print(f"🔍 参考示例: {result['similar_examples'][0]['question']}")
        else:
            print(f"❌ 生成失败")
    
    print(f"\n🎉 演示完成！")

def interactive_mode():
    """交互模式"""
    print("🤖 简化版RAG SQL交互系统")
    print("输入 'quit' 退出，'help' 查看帮助")
    print("=" * 40)
    
    system = SimpleRAGSQLSystem()
    
    while True:
        try:
            question = input("\n请输入您的问题: ").strip()
            
            if not question:
                continue
            
            if question.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            
            if question.lower() == 'help':
                print("""
💡 使用帮助:
- 支持的问题类型：员工查询、部门统计、薪资分析等
- 示例问题：
  * 查询所有员工
  * 技术部有多少员工？
  * 薪资最高的员工是谁？
  * 各部门平均薪资
""")
                continue
            
            result = system.generate_sql(question)
            
            print(f"\n📝 生成的SQL: {result['generated_sql']}")
            print(f"📊 查询结果:\n{result['result_summary']}")
            
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'demo':
            demo_simple_rag_sql()
        elif sys.argv[1] == 'interactive':
            interactive_mode()
        else:
            print("用法: python quick_start_rag_sql.py [demo|interactive]")
    else:
        demo_simple_rag_sql()
