"""
最简化的RAG SQL演示系统
仅使用Python标准库，展示核心概念
"""

import sqlite3
import json
import os
from typing import List, Dict, Any, Tuple

class SimpleRAGSQLDemo:
    """最简化的RAG SQL演示系统"""
    
    def __init__(self):
        self.db_path = "demo_database.db"
        self.examples = self._load_examples()
        self._init_database()
        print("🚀 简化RAG SQL演示系统初始化完成")
    
    def _init_database(self):
        """初始化演示数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建员工表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                department TEXT NOT NULL,
                salary REAL NOT NULL,
                hire_date DATE NOT NULL,
                age INTEGER NOT NULL
            )
        """)
        
        # 插入示例数据
        sample_data = [
            (1, '张三', '技术部', 8000.0, '2022-01-15', 28),
            (2, '李四', '销售部', 6500.0, '2021-03-20', 32),
            (3, '王五', '人事部', 7000.0, '2020-06-10', 35),
            (4, '赵六', '技术部', 9500.0, '2019-08-05', 30),
            (5, '钱七', '财务部', 7500.0, '2021-11-12', 29),
            (6, '孙八', '技术部', 8500.0, '2023-02-01', 26),
            (7, '周九', '销售部', 7200.0, '2022-08-15', 31),
            (8, '吴十', '人事部', 6800.0, '2021-12-01', 33)
        ]
        
        cursor.executemany("""
            INSERT OR REPLACE INTO employees (id, name, department, salary, hire_date, age)
            VALUES (?, ?, ?, ?, ?, ?)
        """, sample_data)
        
        conn.commit()
        conn.close()
        print("✅ 数据库初始化完成，包含8条员工记录")
    
    def _load_examples(self) -> List[Dict]:
        """加载SQL示例"""
        return [
            {
                "question": "查询所有员工信息",
                "sql": "SELECT * FROM employees;",
                "keywords": ["所有", "员工", "全部"],
                "type": "basic_select"
            },
            {
                "question": "查询员工姓名和薪资",
                "sql": "SELECT name, salary FROM employees;",
                "keywords": ["姓名", "薪资", "工资"],
                "type": "basic_select"
            },
            {
                "question": "查询技术部员工",
                "sql": "SELECT * FROM employees WHERE department = '技术部';",
                "keywords": ["技术部", "部门"],
                "type": "filter"
            },
            {
                "question": "统计员工总数",
                "sql": "SELECT COUNT(*) as total_count FROM employees;",
                "keywords": ["总数", "数量", "多少", "统计"],
                "type": "aggregate"
            },
            {
                "question": "查询各部门员工数量",
                "sql": "SELECT department, COUNT(*) as count FROM employees GROUP BY department;",
                "keywords": ["部门", "数量", "统计", "分组"],
                "type": "group_by"
            },
            {
                "question": "查询平均薪资",
                "sql": "SELECT AVG(salary) as avg_salary FROM employees;",
                "keywords": ["平均", "薪资", "工资"],
                "type": "aggregate"
            },
            {
                "question": "查询各部门平均薪资",
                "sql": "SELECT department, AVG(salary) as avg_salary FROM employees GROUP BY department;",
                "keywords": ["部门", "平均", "薪资"],
                "type": "group_by"
            },
            {
                "question": "查询薪资最高的员工",
                "sql": "SELECT * FROM employees WHERE salary = (SELECT MAX(salary) FROM employees);",
                "keywords": ["最高", "薪资", "最大"],
                "type": "subquery"
            },
            {
                "question": "查询薪资大于8000的员工",
                "sql": "SELECT * FROM employees WHERE salary > 8000;",
                "keywords": ["大于", "8000", "薪资"],
                "type": "filter"
            },
            {
                "question": "查询年龄大于30的员工",
                "sql": "SELECT * FROM employees WHERE age > 30;",
                "keywords": ["年龄", "大于", "30"],
                "type": "filter"
            }
        ]
    
    def find_similar_examples(self, question: str, k: int = 3) -> List[Dict]:
        """基于关键词匹配找相似示例"""
        question_lower = question.lower()
        scored_examples = []
        
        for example in self.examples:
            score = 0
            # 关键词匹配得分
            for keyword in example['keywords']:
                if keyword in question_lower:
                    score += 2
            
            # 问题相似度得分
            example_words = set(example['question'].lower().split())
            question_words = set(question_lower.split())
            common_words = example_words.intersection(question_words)
            score += len(common_words)
            
            if score > 0:
                scored_examples.append((score, example))
        
        # 按分数排序
        scored_examples.sort(key=lambda x: x[0], reverse=True)
        return [example for _, example in scored_examples[:k]]
    
    def generate_sql_rule_based(self, question: str, similar_examples: List[Dict]) -> str:
        """基于规则生成SQL"""
        question_lower = question.lower()
        
        # 如果有高分相似示例，直接使用
        if similar_examples:
            best_example = similar_examples[0]
            # 检查是否需要调整
            if "技术部" in question_lower and "技术部" not in best_example['sql']:
                return "SELECT * FROM employees WHERE department = '技术部';"
            elif "销售部" in question_lower and "销售部" not in best_example['sql']:
                return "SELECT * FROM employees WHERE department = '销售部';"
            elif "人事部" in question_lower and "人事部" not in best_example['sql']:
                return "SELECT * FROM employees WHERE department = '人事部';"
            elif "财务部" in question_lower and "财务部" not in best_example['sql']:
                return "SELECT * FROM employees WHERE department = '财务部';"
            else:
                return best_example['sql']
        
        # 基础规则匹配
        if any(word in question_lower for word in ["所有", "全部", "全体"]):
            return "SELECT * FROM employees;"
        elif any(word in question_lower for word in ["数量", "多少", "总数"]):
            return "SELECT COUNT(*) as count FROM employees;"
        elif "平均薪资" in question_lower or "平均工资" in question_lower:
            return "SELECT AVG(salary) as avg_salary FROM employees;"
        elif "最高薪资" in question_lower or "薪资最高" in question_lower:
            return "SELECT * FROM employees WHERE salary = (SELECT MAX(salary) FROM employees);"
        else:
            return "SELECT * FROM employees LIMIT 5;"
    
    def execute_sql(self, sql: str) -> Tuple[List[Dict], str]:
        """执行SQL查询"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(sql)
            columns = [description[0] for description in cursor.description]
            rows = cursor.fetchall()
            
            # 转换为字典列表
            results = []
            for row in rows:
                results.append(dict(zip(columns, row)))
            
            conn.close()
            
            # 格式化结果
            if results:
                result_str = f"查询成功，返回{len(results)}条记录：\n"
                for i, record in enumerate(results[:5], 1):  # 最多显示5条
                    result_str += f"{i}. "
                    result_str += ", ".join([f"{k}: {v}" for k, v in record.items()])
                    result_str += "\n"
                if len(results) > 5:
                    result_str += f"... 还有{len(results)-5}条记录\n"
            else:
                result_str = "查询成功，但没有找到匹配的记录。"
            
            return results, result_str
            
        except Exception as e:
            return [], f"SQL执行失败: {str(e)}"
    
    def process_question(self, question: str) -> Dict[str, Any]:
        """处理用户问题"""
        print(f"\n🔍 处理问题: {question}")
        
        # 1. 查找相似示例
        similar_examples = self.find_similar_examples(question, k=3)
        print(f"📚 找到{len(similar_examples)}个相似示例")
        
        if similar_examples:
            print(f"🎯 最相似示例: {similar_examples[0]['question']}")
        
        # 2. 生成SQL
        sql = self.generate_sql_rule_based(question, similar_examples)
        print(f"📝 生成SQL: {sql}")
        
        # 3. 执行SQL
        results, result_str = self.execute_sql(sql)
        
        return {
            'question': question,
            'generated_sql': sql,
            'execution_results': results,
            'result_summary': result_str,
            'similar_examples': similar_examples,
            'success': len(results) >= 0
        }
    
    def run_demo(self):
        """运行演示"""
        print("\n" + "="*60)
        print("🎯 RAG SQL查询生成系统演示")
        print("="*60)
        
        demo_questions = [
            "查询所有员工信息",
            "技术部有多少员工？",
            "查询各部门的平均薪资",
            "哪个员工的薪资最高？",
            "查询年龄大于30的员工",
            "销售部员工的姓名和薪资"
        ]
        
        for i, question in enumerate(demo_questions, 1):
            print(f"\n问题 {i}: {question}")
            print("-" * 50)
            
            result = self.process_question(question)
            
            if result['success']:
                print(f"✅ 执行成功")
                print(f"📊 结果: {result['result_summary']}")
            else:
                print(f"❌ 执行失败: {result['result_summary']}")
        
        print(f"\n🎉 演示完成！")
        print(f"💡 这个演示展示了RAG系统的核心概念：")
        print(f"   1. 语义检索：根据问题找到相似的SQL示例")
        print(f"   2. 智能生成：基于示例和规则生成SQL查询")
        print(f"   3. 执行验证：执行SQL并返回结果")
    
    def interactive_mode(self):
        """交互模式"""
        print("\n🤖 进入交互模式")
        print("输入问题，系统会生成对应的SQL查询")
        print("输入 'quit' 退出，'help' 查看帮助")
        print("-" * 50)
        
        while True:
            try:
                question = input("\n请输入您的问题: ").strip()
                
                if not question:
                    continue
                
                if question.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                if question.lower() == 'help':
                    print("""
💡 使用帮助:
支持的问题类型：
- 基础查询：查询所有员工、查询员工信息
- 条件查询：技术部员工、薪资大于8000的员工
- 统计查询：员工数量、平均薪资、最高薪资
- 分组查询：各部门员工数量、各部门平均薪资

示例问题：
- 查询所有员工
- 技术部有多少员工？
- 薪资最高的员工是谁？
- 各部门平均薪资
""")
                    continue
                
                result = self.process_question(question)
                
                if result['success']:
                    print(f"✅ 执行成功")
                    print(f"📊 结果: {result['result_summary']}")
                else:
                    print(f"❌ 执行失败: {result['result_summary']}")
                
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出系统")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")

def main():
    """主函数"""
    import sys
    
    demo = SimpleRAGSQLDemo()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'demo':
            demo.run_demo()
        elif sys.argv[1] == 'interactive':
            demo.interactive_mode()
        else:
            print("用法: python simple_rag_demo.py [demo|interactive]")
    else:
        demo.run_demo()

if __name__ == "__main__":
    main()
