"""
RAG SQL系统测试文件
用于验证系统各个组件的功能
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import tempfile
import shutil

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rag_sql_system import (
    DatabaseManager, 
    VectorStoreManager, 
    FewShotExampleManager,
    PromptTemplateManager,
    RAGSQLSystem
)
from rag_sql_config import config

class TestDatabaseManager(unittest.TestCase):
    """测试数据库管理器"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_db.close()
        self.db_manager = DatabaseManager(self.temp_db.name)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    def test_database_initialization(self):
        """测试数据库初始化"""
        self.assertIsNotNone(self.db_manager.schema_info)
        self.assertIn('employees', self.db_manager.schema_info)
        self.assertIn('departments', self.db_manager.schema_info)
        self.assertIn('projects', self.db_manager.schema_info)
    
    def test_schema_description(self):
        """测试数据库结构描述"""
        schema_desc = self.db_manager.get_schema_description()
        self.assertIn('employees', schema_desc)
        self.assertIn('departments', schema_desc)
        self.assertIn('projects', schema_desc)
    
    def test_sql_execution(self):
        """测试SQL执行"""
        sql = "SELECT COUNT(*) as count FROM employees"
        results, result_str = self.db_manager.execute_sql(sql)
        
        self.assertIsInstance(results, list)
        self.assertGreater(len(results), 0)
        self.assertIn('count', results[0])
        self.assertIn('查询成功', result_str)
    
    def test_invalid_sql_execution(self):
        """测试无效SQL执行"""
        sql = "SELECT * FROM non_existent_table"
        results, result_str = self.db_manager.execute_sql(sql)
        
        self.assertEqual(len(results), 0)
        self.assertIn('失败', result_str)

class TestFewShotExampleManager(unittest.TestCase):
    """测试Few-shot示例管理器"""
    
    def setUp(self):
        """测试前准备"""
        self.example_manager = FewShotExampleManager()
    
    def test_default_examples_loaded(self):
        """测试默认示例加载"""
        examples = self.example_manager.get_all_examples()
        self.assertGreater(len(examples), 0)
        
        # 检查示例结构
        for example in examples[:3]:
            self.assertIn('question', example)
            self.assertIn('sql', example)
            self.assertIn('explanation', example)
            self.assertIn('difficulty', example)
    
    def test_add_example(self):
        """测试添加示例"""
        initial_count = len(self.example_manager.get_all_examples())
        
        self.example_manager.add_example(
            question="测试问题",
            sql="SELECT * FROM test;",
            explanation="测试说明",
            difficulty="easy"
        )
        
        new_count = len(self.example_manager.get_all_examples())
        self.assertEqual(new_count, initial_count + 1)
    
    def test_get_examples_by_difficulty(self):
        """测试按难度获取示例"""
        easy_examples = self.example_manager.get_examples_by_difficulty('easy')
        medium_examples = self.example_manager.get_examples_by_difficulty('medium')
        hard_examples = self.example_manager.get_examples_by_difficulty('hard')
        
        self.assertGreater(len(easy_examples), 0)
        self.assertGreater(len(medium_examples), 0)
        self.assertGreater(len(hard_examples), 0)
        
        # 验证难度分类正确
        for example in easy_examples:
            self.assertEqual(example['difficulty'], 'easy')

class TestPromptTemplateManager(unittest.TestCase):
    """测试Prompt模板管理器"""
    
    def setUp(self):
        """测试前准备"""
        self.prompt_manager = PromptTemplateManager()
    
    def test_prompt_template_creation(self):
        """测试Prompt模板创建"""
        self.assertIsNotNone(self.prompt_manager.system_prompt_template)
        self.assertIsNotNone(self.prompt_manager.human_prompt_template)
        
        # 检查模板内容
        self.assertIn('SQL查询生成助手', self.prompt_manager.system_prompt_template)
        self.assertIn('用户问题', self.prompt_manager.human_prompt_template)
    
    def test_build_prompt(self):
        """测试构建Prompt"""
        question = "查询所有员工"
        schema_info = "表: employees (id, name, salary)"
        similar_examples = [
            {
                'question': '查询员工信息',
                'sql': 'SELECT * FROM employees;',
                'explanation': '查询所有员工信息'
            }
        ]
        
        chat_prompt = self.prompt_manager.build_prompt(
            question, schema_info, similar_examples
        )
        
        self.assertIsNotNone(chat_prompt)

class TestRAGSQLSystemIntegration(unittest.TestCase):
    """测试RAG SQL系统集成"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.temp_db = os.path.join(self.temp_dir, 'test.db')
        
        # Mock OpenAI API
        self.openai_patcher = patch('rag_sql_system.ChatOpenAI')
        self.mock_openai = self.openai_patcher.start()
        
        # Mock Embeddings
        self.embeddings_patcher = patch('rag_sql_system.OpenAIEmbeddings')
        self.mock_embeddings = self.embeddings_patcher.start()
        
        # Mock ChromaDB
        self.chroma_patcher = patch('rag_sql_system.Chroma')
        self.mock_chroma = self.chroma_patcher.start()
        
        # 设置mock返回值
        mock_llm_instance = MagicMock()
        mock_llm_instance.run.return_value = "SELECT * FROM employees;"
        self.mock_openai.return_value = mock_llm_instance
        
        mock_vectorstore = MagicMock()
        mock_vectorstore.similarity_search.return_value = []
        self.mock_chroma.return_value = mock_vectorstore
    
    def tearDown(self):
        """测试后清理"""
        self.openai_patcher.stop()
        self.embeddings_patcher.stop()
        self.chroma_patcher.stop()
        
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    @patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'})
    def test_system_initialization(self):
        """测试系统初始化"""
        try:
            system = RAGSQLSystem(db_path=self.temp_db)
            self.assertIsNotNone(system.db_manager)
            self.assertIsNotNone(system.example_manager)
            self.assertIsNotNone(system.prompt_manager)
        except Exception as e:
            self.fail(f"系统初始化失败: {e}")
    
    @patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'})
    def test_sql_generation_workflow(self):
        """测试SQL生成工作流"""
        system = RAGSQLSystem(db_path=self.temp_db)
        
        # Mock LLM chain
        with patch('rag_sql_system.LLMChain') as mock_chain_class:
            mock_chain = MagicMock()
            mock_chain.run.return_value = "SELECT name, salary FROM employees;"
            mock_chain_class.return_value = mock_chain
            
            # Mock callback
            with patch('rag_sql_system.get_openai_callback') as mock_callback:
                mock_cb = MagicMock()
                mock_cb.total_tokens = 100
                mock_cb.prompt_tokens = 80
                mock_cb.completion_tokens = 20
                mock_cb.total_cost = 0.001
                mock_callback.return_value.__enter__.return_value = mock_cb
                
                result = system.generate_sql("查询所有员工的姓名和薪资")
                
                self.assertIsInstance(result, dict)
                self.assertIn('question', result)
                self.assertIn('generated_sql', result)
                self.assertIn('success', result)

class TestSystemConfiguration(unittest.TestCase):
    """测试系统配置"""
    
    def test_config_validation(self):
        """测试配置验证"""
        # 测试无API密钥的情况
        with patch.dict(os.environ, {}, clear=True):
            self.assertFalse(config.validate_config())
        
        # 测试有API密钥的情况
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            # 重新加载配置
            config.OPENAI_API_KEY = 'test-key'
            self.assertTrue(config.validate_config())
    
    def test_config_methods(self):
        """测试配置方法"""
        openai_config = config.get_openai_config()
        self.assertIn('model_name', openai_config)
        self.assertIn('temperature', openai_config)
        
        db_config = config.get_database_config()
        self.assertIn('path', db_config)
        
        chroma_config = config.get_chroma_config()
        self.assertIn('persist_directory', chroma_config)

def run_basic_functionality_test():
    """运行基础功能测试（不需要API密钥）"""
    print("🧪 运行基础功能测试...")
    
    # 测试数据库管理器
    print("1. 测试数据库管理器...")
    db_manager = DatabaseManager()
    schema_desc = db_manager.get_schema_description()
    print(f"   ✅ 数据库结构加载成功，包含{len(db_manager.schema_info)}个表")
    
    # 测试示例管理器
    print("2. 测试示例管理器...")
    example_manager = FewShotExampleManager()
    examples = example_manager.get_all_examples()
    print(f"   ✅ 示例加载成功，共{len(examples)}个示例")
    
    # 测试Prompt模板管理器
    print("3. 测试Prompt模板管理器...")
    prompt_manager = PromptTemplateManager()
    print("   ✅ Prompt模板创建成功")
    
    # 测试SQL执行
    print("4. 测试SQL执行...")
    results, result_str = db_manager.execute_sql("SELECT COUNT(*) as count FROM employees")
    print(f"   ✅ SQL执行成功，返回{len(results)}条记录")
    
    print("\n🎉 基础功能测试完成！")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'basic':
        # 运行基础功能测试
        run_basic_functionality_test()
    else:
        # 运行完整单元测试
        unittest.main()
