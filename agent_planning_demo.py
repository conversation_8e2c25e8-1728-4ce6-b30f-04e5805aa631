"""
智能体流程规划演示
展示 CodeAgent 的智能规划和分步执行能力
"""

from codeAgent import IntelligentCodeAgent
import time

def demo_planning_capabilities():
    """演示智能规划能力"""
    print("🤖 智能体流程规划演示\n")
    
    # 创建智能代码助手
    agent = IntelligentCodeAgent()
    
    # 演示不同复杂度的任务
    demo_tasks = [
        {
            "name": "简单任务",
            "description": "创建一个计算斐波那契数列的函数",
            "complexity": "low"
        },
        {
            "name": "中等任务", 
            "description": "创建一个数据分析模块，包含数据读取、清洗、分析和可视化功能",
            "complexity": "medium"
        },
        {
            "name": "复杂任务",
            "description": "开发一个完整的 Flask Web 应用，包含用户认证、数据库操作、API 接口和前端页面",
            "complexity": "high"
        }
    ]
    
    for i, task in enumerate(demo_tasks, 1):
        print(f"\n{'='*60}")
        print(f"📋 演示 {i}: {task['name']} (复杂度: {task['complexity']})")
        print(f"任务描述: {task['description']}")
        print('='*60)
        
        # 使用智能规划模式执行
        print("\n🧠 使用智能规划模式:")
        start_time = time.time()
        
        try:
            result = agent.run(task['description'], use_planning=True)
            planning_time = time.time() - start_time
            
            print(f"\n📊 规划模式结果 (耗时: {planning_time:.2f}秒):")
            print(result[:500] + "..." if len(result) > 500 else result)
            
        except Exception as e:
            print(f"❌ 规划模式执行失败: {e}")
        
        print("\n" + "-"*40)
        
        # 对比：使用直接执行模式
        print("\n⚡ 使用直接执行模式:")
        start_time = time.time()
        
        try:
            result = agent.run(task['description'], use_planning=False)
            direct_time = time.time() - start_time
            
            print(f"\n📊 直接模式结果 (耗时: {direct_time:.2f}秒):")
            print(result[:500] + "..." if len(result) > 500 else result)
            
        except Exception as e:
            print(f"❌ 直接模式执行失败: {e}")
        
        print(f"\n💡 性能对比:")
        if 'planning_time' in locals() and 'direct_time' in locals():
            print(f"   规划模式: {planning_time:.2f}秒")
            print(f"   直接模式: {direct_time:.2f}秒")
            if planning_time < direct_time:
                print("   ✅ 规划模式更高效")
            else:
                print("   ⚠️ 直接模式更快，但规划模式提供更好的控制")

def demo_task_decomposition():
    """演示任务分解能力"""
    print("\n\n🔧 === 任务分解能力演示 ===\n")
    
    agent = IntelligentCodeAgent()
    
    complex_task = """
    创建一个在线图书管理系统，要求：
    1. 用户可以注册和登录
    2. 管理员可以添加、删除、修改图书信息
    3. 用户可以搜索、浏览、借阅图书
    4. 系统记录借阅历史
    5. 提供图书推荐功能
    6. 支持图书评论和评分
    """
    
    print("📚 复杂任务:")
    print(complex_task)
    
    # 分析任务复杂度
    task_analysis = agent._analyze_task_complexity(complex_task)
    print(f"\n🔍 任务分析结果:")
    print(f"   复杂度: {task_analysis['complexity']}")
    print(f"   所需工具: {', '.join(task_analysis['required_tools'])}")
    print(f"   预估步骤: {task_analysis['estimated_steps']}")
    
    # 创建执行计划
    execution_plan = agent._create_execution_plan(complex_task, task_analysis)
    print(f"\n📋 执行计划:")
    print(f"   总步骤数: {len(execution_plan['steps'])}")
    print(f"   预估时间: {execution_plan['estimated_time']} 分钟")
    print(f"   检查点: {execution_plan['checkpoints']}")
    
    print(f"\n📝 详细步骤:")
    for i, step in enumerate(execution_plan['steps'], 1):
        print(f"   {i}. {step['name']}")
        print(f"      描述: {step['description']}")
        print(f"      工具: {', '.join(step['tools'])}")
        print()

def demo_adaptive_planning():
    """演示自适应规划能力"""
    print("\n\n🎯 === 自适应规划演示 ===\n")
    
    agent = IntelligentCodeAgent()
    
    # 模拟一个可能遇到问题的任务
    adaptive_task = "创建一个机器学习项目，包含数据预处理、模型训练、评估和部署"
    
    print("🤖 自适应任务:")
    print(adaptive_task)
    
    print("\n🔄 模拟执行过程中的适应性调整:")
    
    # 模拟执行步骤和可能的问题
    simulation_steps = [
        {"step": 1, "name": "数据收集", "status": "success", "issue": None},
        {"step": 2, "name": "数据预处理", "status": "partial_success", "issue": "数据质量问题"},
        {"step": 3, "name": "特征工程", "status": "success", "issue": None},
        {"step": 4, "name": "模型选择", "status": "failure", "issue": "依赖库缺失"},
        {"step": 5, "name": "模型训练", "status": "pending", "issue": None},
    ]
    
    for step_info in simulation_steps:
        print(f"\n📍 步骤 {step_info['step']}: {step_info['name']}")
        print(f"   状态: {step_info['status']}")
        
        if step_info['issue']:
            print(f"   问题: {step_info['issue']}")
            
            # 模拟自适应调整
            if step_info['status'] == "partial_success":
                print("   🔧 自适应调整: 添加数据清洗步骤")
            elif step_info['status'] == "failure":
                print("   🔧 自适应调整: 切换到替代模型库")
                print("   📋 更新计划: 添加依赖安装步骤")
        else:
            print("   ✅ 执行顺利")

def demo_memory_integration():
    """演示记忆集成在规划中的作用"""
    print("\n\n🧠 === 记忆集成演示 ===\n")
    
    agent = IntelligentCodeAgent()
    
    # 模拟历史经验
    print("📚 模拟历史经验学习:")
    
    historical_tasks = [
        "创建一个简单的计算器应用",
        "开发一个待办事项管理器",
        "构建一个博客系统"
    ]
    
    for task in historical_tasks:
        print(f"   ✅ 已完成: {task}")
        # 模拟存储经验
        agent.memory_manager.store_knowledge(
            "completed_projects",
            task.replace(" ", "_"),
            {
                "task": task,
                "complexity": "medium",
                "success": True,
                "lessons_learned": ["模块化设计很重要", "测试驱动开发有效"]
            }
        )
    
    # 新任务利用历史经验
    new_task = "创建一个在线商店系统"
    print(f"\n🎯 新任务: {new_task}")
    
    # 搜索相关经验
    relevant_history = agent.memory_manager.search_conversations(new_task, limit=3)
    print(f"\n🔍 相关历史经验: {len(relevant_history)} 条")
    
    # 获取项目模式
    project_patterns = agent.memory_manager.retrieve_knowledge("completed_projects")
    if project_patterns:
        print(f"📋 可参考的项目模式: {len(project_patterns)} 个")
        
        print("\n💡 基于历史经验的规划优化:")
        print("   - 采用模块化架构设计")
        print("   - 优先实现核心功能")
        print("   - 集成自动化测试")
        print("   - 考虑用户体验设计")

def main():
    """主演示函数"""
    print("🚀 智能体流程规划全面演示")
    print("="*60)
    
    try:
        # 1. 基础规划能力演示
        demo_planning_capabilities()
        
        # 2. 任务分解演示
        demo_task_decomposition()
        
        # 3. 自适应规划演示
        demo_adaptive_planning()
        
        # 4. 记忆集成演示
        demo_memory_integration()
        
        print("\n\n🎉 所有演示完成！")
        
        print("\n📊 智能体流程规划的核心优势:")
        print("✅ 自动任务分解 - 将复杂任务分解为可管理的步骤")
        print("✅ 智能工具选择 - 根据任务特点选择最适合的工具")
        print("✅ 自适应调整 - 根据执行情况动态调整计划")
        print("✅ 记忆驱动优化 - 利用历史经验改进规划质量")
        print("✅ 检查点管理 - 提供安全的回滚和恢复机制")
        print("✅ 并行执行优化 - 识别可并行的任务提高效率")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        print("💡 这可能是由于模型配置或依赖问题导致的")

if __name__ == "__main__":
    main()
